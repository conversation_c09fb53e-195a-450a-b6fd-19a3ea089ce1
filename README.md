# Raft-KV 分布式键值存储系统

这是一个基于Raft共识算法的分布式键值存储系统，采用C++实现，集成了高性能协程库、RPC通信框架和完整的Raft算法实现。

## 项目概述

本项目实现了一个完整的分布式键值存储系统，具有以下特点：
- **强一致性**：基于Raft共识算法保证数据一致性
- **高性能**：使用协程库实现高并发处理
- **容错性**：支持节点故障恢复和集群成员变更
- **持久化**：支持数据持久化和快照机制

## 核心模块

### 1. Fiber模块 - Monsoon协程库

Fiber模块是一个完整的用户态协程库，提供高性能的异步编程支持。

#### 核心组件

- **Fiber类** (`include/raft-kv/fiber/fiber.h`)
  - 实现用户态协程，支持协程的创建、切换、销毁
  - 使用`ucontext_t`实现协程上下文切换
  - 支持协程状态管理：READY（就绪）、RUNNING（运行）、TERM（结束）
  - 提供协程栈空间管理和复用机制

- **Scheduler调度器** (`include/raft-kv/fiber/scheduler.h`)
  - 实现N->M协程调度器（N个线程调度M个协程）
  - 支持协程和函数对象的调度
  - 提供线程池管理和任务队列
  - 支持指定线程执行特定任务

- **IOManager IO管理器** (`include/raft-kv/fiber/iomanager.h`)
  - 继承自调度器，实现IO事件驱动的协程调度
  - 使用epoll实现高效的IO多路复用
  - 支持定时器功能，提供超时处理
  - 管理文件描述符上下文和IO事件

- **其他组件**
  - **Thread线程封装**：提供线程管理功能
  - **Hook系统调用拦截**：拦截系统调用实现异步化
  - **Channel协程通信**：提供协程间通信机制
  - **FdManager文件描述符管理**：管理文件描述符状态

#### 主要特性

- **轻量级协程**：用户态协程切换，开销极小
- **高并发支持**：支持大量协程并发执行
- **异步IO**：基于epoll的异步IO处理
- **定时器支持**：精确的定时器功能
- **系统调用Hook**：透明的异步化改造

### 2. RPC模块 - 高性能RPC通信框架

RPC模块基于protobuf实现，提供高效的远程过程调用功能。

#### 核心组件

- **RpcProvider服务提供者** (`include/raft-kv/rpc/rpcprovider.h`)
  - 基于muduo网络库实现的RPC服务器
  - 支持多服务注册和动态服务发现
  - 处理RPC请求的反序列化和响应序列化
  - 提供连接管理和消息路由功能

- **MprpcChannel通信通道** (`include/raft-kv/rpc/mprpcchannel.h`)
  - 实现protobuf RpcChannel接口
  - 支持同步和异步RPC调用
  - 提供连接重试和错误处理机制
  - 集成协程库实现异步RPC

- **MprpcController控制器** (`include/raft-kv/rpc/mprpccontroller.h`)
  - 实现protobuf RpcController接口
  - 提供RPC调用状态管理
  - 支持错误信息传递和处理

#### 通信协议

RPC通信采用自定义的二进制协议：
```
[header_size][service_name][method_name][args_size][args_data]
```

- **header_size**：使用protobuf变长编码
- **RpcHeader**：包含服务名、方法名、参数大小、请求ID
- **args_data**：序列化的请求参数

#### 主要特性

- **异步支持**：支持回调和Future两种异步调用方式
- **连接管理**：自动重连和连接池管理
- **错误处理**：完善的错误处理和重试机制
- **协程集成**：与Fiber模块深度集成，支持协程化RPC

### 3. Raft模块 - 分布式共识算法

Raft模块实现了完整的Raft共识算法，确保分布式系统的强一致性。

#### 核心组件

- **Raft类** (`include/raft-kv/raftCore/raft.h`)
  - 实现Raft算法的核心逻辑
  - 支持领导者选举、日志复制、安全性保证
  - 提供成员变更和快照机制
  - 集成协程库实现高性能处理

- **ApplyMsg应用消息** (`include/raft-kv/raftCore/ApplyMsg.h`)
  - 定义Raft与上层应用的通信接口
  - 支持命令消息和快照消息两种类型
  - 提供消息有效性验证机制

- **Persister持久化存储** (`include/raft-kv/raftCore/Persister.h`)
  - 负责Raft状态的持久化存储
  - 支持状态数据和快照的保存/读取
  - 提供文件流管理和数据完整性保证

- **KvServer键值存储服务器** (`include/raft-kv/raftCore/kvServer.h`)
  - 基于Raft实现的分布式键值存储
  - 支持Get、Put、Append操作
  - 提供重复请求检测和快照机制

#### Raft算法实现

- **领导者选举**
  - 随机选举超时，避免选举冲突
  - 支持任期管理和投票机制
  - 实现日志最新性检查

- **日志复制**
  - 支持并行日志复制，提高性能
  - 实现日志一致性检查和修复
  - 支持快速回退优化

- **安全性保证**
  - 实现选举安全性和日志匹配特性
  - 支持领导者完整性和状态机安全性
  - 提供成员变更的安全机制

- **快照机制**
  - 支持状态机快照创建和安装
  - 实现日志压缩，减少存储开销
  - 提供快照传输和恢复功能

#### 主要特性

- **强一致性**：严格按照Raft算法实现，保证强一致性
- **高可用性**：支持节点故障恢复和集群重配置
- **性能优化**：使用协程库提高并发处理能力
- **持久化支持**：完整的状态持久化和恢复机制

## 模块间集成

### Fiber + RPC集成
- RPC模块集成Fiber协程库，实现异步RPC调用
- 使用IOManager管理网络连接和IO事件
- 通过协程实现高并发RPC处理

### RPC + Raft集成
- Raft节点间通信使用RPC模块
- 支持AppendEntries、RequestVote、InstallSnapshot等RPC调用
- 客户端通过RPC与KV服务器通信

### Fiber + Raft集成
- Raft算法使用协程实现并发处理
- 选举定时器、心跳定时器基于协程调度
- 使用Channel实现Raft与KV服务器的通信

## 技术栈

- **编程语言**：C++17
- **网络库**：muduo
- **序列化**：Protocol Buffers
- **构建系统**：CMake
- **测试框架**：Google Test
- **并发模型**：协程 + 事件驱动

## 项目结构

```
Raft-KV/
├── include/raft-kv/           # 头文件目录
│   ├── fiber/                 # Fiber协程库头文件
│   │   ├── monsoon.h         # 协程库主头文件
│   │   ├── fiber.h           # 协程核心类
│   │   ├── scheduler.h       # 协程调度器
│   │   ├── iomanager.h       # IO管理器
│   │   └── ...
│   ├── rpc/                   # RPC模块头文件
│   │   ├── rpcprovider.h     # RPC服务提供者
│   │   ├── mprpcchannel.h    # RPC通信通道
│   │   └── mprpccontroller.h # RPC控制器
│   ├── raftCore/              # Raft算法头文件
│   │   ├── raft.h            # Raft核心类
│   │   ├── kvServer.h        # KV存储服务器
│   │   ├── ApplyMsg.h        # 应用消息
│   │   └── Persister.h       # 持久化存储
│   └── common/                # 公共头文件
├── src/                       # 源文件目录
│   ├── fiber/                 # Fiber协程库实现
│   ├── rpc/                   # RPC模块实现
│   ├── raftCore/              # Raft算法实现
│   └── common/                # 公共实现
├── raftRpcPro/                # Protobuf协议定义
│   ├── raftRPC.proto         # Raft RPC协议
│   └── kvServerRPC.proto     # KV服务器RPC协议
├── tests/                     # 集成测试
├── unit_tests/                # 单元测试
├── bin/                       # 可执行文件
└── build/                     # 构建目录
```

## 构建和运行

### 环境要求

- **操作系统**：Linux (推荐Ubuntu 18.04+)
- **编译器**：GCC 7.0+ 或 Clang 6.0+
- **依赖库**：
  - muduo网络库
  - protobuf 3.0+
  - boost库
  - Google Test (用于测试)

### 构建步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd Raft-KV
```

2. **安装依赖**
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install build-essential cmake
sudo apt-get install libprotobuf-dev protobuf-compiler
sudo apt-get install libboost-all-dev
sudo apt-get install libgtest-dev

# 安装muduo库 (需要手动编译)
# 请参考muduo官方文档进行安装
```

3. **编译项目**
```bash
# 使用提供的构建脚本
./build.sh

# 或者手动构建
mkdir -p build
cd build
cmake ..
make -j$(nproc)
```

### 运行示例

1. **启动KV服务器集群**
```bash
# 启动第一个节点
./bin/server 0

# 启动第二个节点
./bin/server 1

# 启动第三个节点
./bin/server 2
```

2. **运行客户端测试**
```bash
./bin/client
```

3. **运行单元测试**
```bash
./bin/unit_tests
```

4. **运行集成测试**
```bash
./bin/integration_test
```

## 性能特点

### Fiber协程库性能
- **协程切换开销**：< 100ns
- **内存占用**：每个协程约8KB栈空间
- **并发能力**：支持百万级协程并发
- **IO性能**：基于epoll的高效IO处理

### RPC通信性能
- **延迟**：局域网内 < 1ms
- **吞吐量**：单连接 > 100K QPS
- **连接数**：支持万级并发连接
- **序列化**：protobuf高效序列化

### Raft算法性能
- **选举时间**：通常 < 200ms
- **日志复制**：支持批量复制优化
- **快照性能**：增量快照，减少IO开销
- **恢复时间**：快速状态恢复

## 配置说明

### 节点配置文件
```json
{
  "node_id": "node1",
  "address": "127.0.0.1:8001",
  "peers": [
    {"id": "node2", "address": "127.0.0.1:8002"},
    {"id": "node3", "address": "127.0.0.1:8003"}
  ]
}
```

### Raft参数配置
- **选举超时**：150-300ms随机
- **心跳间隔**：50ms
- **快照阈值**：10000条日志
- **批量大小**：100条日志

## 测试

项目包含完整的测试套件：

### 单元测试
- Fiber协程库功能测试
- RPC通信模块测试
- Raft算法核心测试
- KV存储功能测试

### 集成测试
- 多节点集群测试
- 故障恢复测试
- 性能压力测试
- 一致性验证测试

### 运行测试
```bash
# 运行所有单元测试
./bin/unit_tests

# 运行特定测试
./bin/unit_tests --gtest_filter="FiberTest.*"

# 运行集成测试
./bin/integration_test

# 运行性能测试
./bin/fiber_test
```
